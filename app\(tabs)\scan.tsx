import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { LinearGradient } from 'expo-linear-gradient';
import { Camera, FlipHorizontal, X, Shield, CircleCheck as CheckCircle, TriangleAlert as AlertTriangle, Info, Sparkles } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import { useNavigation } from '@react-navigation/native';
import WebView from 'react-native-webview';

export default function ScanScreen() {
  const [facing, setFacing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const [isScanning, setIsScanning] = useState(false);
  const [scanResult, setScanResult] = useState<any>(null);
  const [num, setNum] = useState<string | null>(null);
  const [webViewUrl, setWebViewUrl] = useState<string | null>(null);
  const router = useRouter();
  const navigation = useNavigation();

  if (!permission) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <LinearGradient
            colors={['#667eea', '#764ba2']}
            style={styles.loadingIcon}>
            <Camera size={32} color="#FFFFFF" strokeWidth={2} />
          </LinearGradient>
          <Text style={styles.loadingText}>Chargement de la caméra...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!permission.granted) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.permissionContainer}>
          <LinearGradient
            colors={['#667eea', '#764ba2']}
            style={styles.permissionIcon}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}>
            <Camera size={56} color="#FFFFFF" strokeWidth={1.5} />
            <Sparkles size={24} color="#FFFFFF" style={styles.sparkleIcon} />
          </LinearGradient>
          <Text style={styles.permissionTitle}>Accès à la caméra requis</Text>
          <Text style={styles.permissionText}>
            Nous avons besoin de l'accès à votre caméra pour scanner le QR Code et vérifier
            la certification halal des produits en temps réel.
          </Text>
          <TouchableOpacity style={styles.permissionButton} onPress={requestPermission}>
            <LinearGradient
              colors={['#667eea', '#764ba2']}
              style={styles.buttonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}>
              <Text style={styles.permissionButtonText}>Autoriser l'accès</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const toggleCameraFacing = () => {
    setFacing((current) => (current === 'back' ? 'front' : 'back'));
  };

  const handleBarcodeScanned = ({ data }: any) => {
    if (isScanning) return;
    setIsScanning(true);

    if (data) {
      // Check if the QR code contains a search parameter for certitracehalal786.fr
      const searchMatch = data.match(/search=([^&]+)/);
      if (searchMatch && searchMatch[1]) {
        const scannedNum = decodeURIComponent(searchMatch[1]);
        setNum(scannedNum);  // Store num to update the WebView
        setWebViewUrl(`https://certitracehalal786.fr/home_search?search=${scannedNum}`);

        setTimeout(() => {
          setIsScanning(false);
        }, 500);

        return;
      }

      // Check if the QR code matches the controle-certitrace.fr pattern
      if (data.includes('https://certitracehalal786.fr', 'https://directory.certitracehalal786.fr', 'https://controle-certitrace.fr' )) {
        setWebViewUrl(data);
        setNum(null); // Clear num since we're navigating to a different URL

        setTimeout(() => {
          setIsScanning(false);
        }, 500);

        return;
      }
    }

    setTimeout(() => {
      setScanResult({
        data: data || 'Code QR non reconnu',
        message: 'Ce code QR ne correspond pas à un produit certiTracehalal786 dans notre base de données.'
      });
      setIsScanning(false);
    }, 1500);
  };


  const resetScan = () => {
    setScanResult(null);
    setIsScanning(false);
    setNum(null);
    setWebViewUrl(null);
  };

  const getStatusColor = (status: string): [string, string] => {
    // If we have a num (certitracehalal786.fr search), show pending/processing colors
    if (num) {
      return ['#F59E0B', '#D97706']; // Orange for processing
    }

    // If we have a webViewUrl (controle-certitrace.fr), show info/navigation colors
    if (webViewUrl) {
      return ['#10B981', '#059669']; // Blue for navigation
    }

    switch (status) {
      case 'verified': return ['#10B981', '#059669'];
      case 'not_certified': return ['#EF4444', '#DC2626'];
      case 'pending': return ['#F59E0B', '#D97706'];
      default: return ['#6B7280', '#4B5563'];
    }
  };

  const getStatusIcon = (status: string) => {
    // If we have a num (certitracehalal786.fr search) or webViewUrl (controle-certitrace.fr), show verified
    if (num || webViewUrl) {
      return CheckCircle;
    }

    switch (status) {
      case 'verified': return CheckCircle;
      case 'not_certified': return AlertTriangle;
      case 'pending': return Info;
      default: return Info;
    }
  };

  const getStatusText = (status: string) => {
    // If we have a num (certitracehalal786.fr search), show specific message
    if (num) {
      return ('Produit Trouvé - Vérification en cours');
    }

    // If we have a webViewUrl (controle-certitrace.fr), show navigation message
    if (webViewUrl) {
      return 'Navigation vers le site de contrôle';
    }

  };



  if (scanResult) {
    const StatusIcon = getStatusIcon(scanResult.status);
    const statusColors = getStatusColor(scanResult.status);

    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.resultContainer}>
          <TouchableOpacity style={styles.closeButton} onPress={resetScan}>
            <X size={24} color="#64748B" strokeWidth={2} />
          </TouchableOpacity>

          <View style={styles.resultCard}>
            <LinearGradient
              colors={statusColors}
              style={styles.statusHeader}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}>
              <StatusIcon size={28} color="#FFFFFF" strokeWidth={2} />
              <Text style={styles.statusText}>{getStatusText(scanResult.status)}</Text>
            </LinearGradient>
            

            <View style={styles.productDetails}>
              <Text style={styles.productName}>{scanResult.data}</Text>

              

              {webViewUrl ? (
                <WebView
                  source={{ uri: webViewUrl }}
                  style={{ flex: 1 }}
                />
              ) : (
                null
              )}
              <TouchableOpacity style={styles.scanAgainButton} onPress={resetScan}>
                <LinearGradient
                  colors={['#667eea', '#764ba2']}
                  style={styles.buttonGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}>
                  <Text style={styles.scanAgainText}>Scanner un autre produit</Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Scanner un produit</Text>
        <Text style={styles.headerSubtitle}>
          Pointez votre caméra sur le QR Code du produit pour vérifier sa certification halal
        </Text>
      </View>

      <View style={styles.cameraContainer}>
        <CameraView
          style={styles.camera}
          facing={facing}
          onBarcodeScanned={handleBarcodeScanned}
        >
          <View style={styles.overlay}>
            <View style={styles.scanFrame}>
              <View style={[styles.corner, styles.topLeft]} />
              <View style={[styles.corner, styles.topRight]} />
              <View style={[styles.corner, styles.bottomLeft]} />
              <View style={[styles.corner, styles.bottomRight]} />
              
              <LinearGradient
                colors={['#667eea', '#764ba2']}
                style={styles.scanLine}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              />
            </View>
          </View>
          
          <View style={styles.controls}>
            <TouchableOpacity style={styles.controlButton} onPress={toggleCameraFacing}>
              <LinearGradient
                colors={['rgba(0,0,0,0.6)', 'rgba(0,0,0,0.8)']}
                style={styles.controlButtonGradient}>
                <FlipHorizontal size={24} color="#FFFFFF" strokeWidth={2} />
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </CameraView>
      </View>

      {isScanning && (
        <View style={styles.scanningOverlay}>
          <View style={styles.scanningContainer}>
            <LinearGradient
              colors={['#667eea', '#764ba2']}
              style={styles.scanningIcon}>
              <Shield size={40} color="#FFFFFF" strokeWidth={2} />
            </LinearGradient>
            <Text style={styles.scanningText}>Vérification en cours...</Text>
            <Text style={styles.scanningSubtext}>
              Nous analysons le produit et vérifions sa certification halal
            </Text>
          </View>
        </View>
      )}
      
      

      <View style={styles.instructions}>
      {num && (
        <Text onPress={() => router.push(`/certifications?search=${num}`)} style={styles.instructionText}>N° de LOT : {num}</Text>
      )}
      {!num && webViewUrl && (
        <Text onPress={() => router.push('https://certitracehalal786.fr/')} style={styles.instructionText}>Lien : {webViewUrl}</Text>
      )}
    </View>
    
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8FAFC',
  },
  loadingIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  loadingText: {
    fontSize: 16,
    color: '#64748B',
    fontFamily: 'Inter-Medium',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    backgroundColor: '#F8FAFC',
  },
  permissionIcon: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
    position: 'relative',
  },
  sparkleIcon: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  permissionTitle: {
    fontSize: 28,
    color: '#0F172A',
    fontFamily: 'Inter-Bold',
    marginBottom: 16,
    textAlign: 'center',
    letterSpacing: -0.5,
  },
  permissionText: {
    fontSize: 16,
    color: '#64748B',
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 40,
    paddingHorizontal: 20,
  },
  permissionButton: {
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  buttonGradient: {
    paddingHorizontal: 40,
    paddingVertical: 18,
    alignItems: 'center',
  },
  permissionButtonText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    letterSpacing: -0.3,
  },
  header: {
    padding: 24,
    backgroundColor: '#F8FAFC',
  },
  headerTitle: {
    fontSize: 28,
    color: '#0F172A',
    fontFamily: 'Inter-Bold',
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#64748B',
    fontFamily: 'Inter-Regular',
    lineHeight: 24,
  },
  cameraContainer: {
    flex: 1,
    margin: 24,
    borderRadius: 24,
    overflow: 'hidden',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanFrame: {
    width: 300,
    height: 300,
    position: 'relative',
  },
  corner: {
    position: 'absolute',
    width: 50,
    height: 50,
    borderColor: '#FFFFFF',
    borderWidth: 4,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
    borderTopLeftRadius: 12,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
    borderTopRightRadius: 12,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
    borderBottomLeftRadius: 12,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    borderBottomRightRadius: 12,
  },
  scanLine: {
    position: 'absolute',
    top: '50%',
    left: 30,
    right: 30,
    height: 3,
    borderRadius: 2,
    opacity: 0.9,
  },
  controls: {
    position: 'absolute',
    bottom: 40,
    right: 40,
  },
  controlButton: {
    borderRadius: 28,
    overflow: 'hidden',
  },
  controlButtonGradient: {
    width: 56,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanningOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanningContainer: {
    backgroundColor: '#FFFFFF',
    padding: 48,
    borderRadius: 24,
    alignItems: 'center',
    margin: 40,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.3,
    shadowRadius: 24,
    elevation: 12,
  },
  scanningIcon: {
    width: 96,
    height: 96,
    borderRadius: 48,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  scanningText: {
    fontSize: 20,
    color: '#0F172A',
    fontFamily: 'Inter-Bold',
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  scanningSubtext: {
    fontSize: 16,
    color: '#64748B',
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    lineHeight: 24,
  },
  instructions: {
    padding: 24,
    backgroundColor: '#F8FAFC',
  },
  instructionText: {
    fontSize: 16,
    color: '#64748B',
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    lineHeight: 24,
  },
  resultContainer: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  closeButton: {
    position: 'absolute',
    top: 24,
    right: 24,
    zIndex: 1,
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  resultCard: {
    margin: 24,
    marginTop: 96,
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 12,
  },
  statusHeader: {
    padding: 32,
    alignItems: 'center',
  },
  statusTitle: {
    fontSize: 20,
    color: '#FFFFFF',
    fontFamily: 'Inter-Bold',
    marginTop: 16,
    textAlign: 'center',
    letterSpacing: -0.5,
  },
  productDetails: {
    padding: 32,
  },
  productName: {
    fontSize: 24,
    color: '#0F172A',
    fontFamily: 'Inter-Bold',
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  productBrand: {
    fontSize: 18,
    color: '#64748B',
    fontFamily: 'Inter-Medium',
    marginBottom: 32,
  },
  certificationInfo: {
    backgroundColor: '#F0FDF4',
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
  },
  certRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  certLabel: {
    fontSize: 14,
    color: '#64748B',
    fontFamily: 'Inter-Regular',
    flex: 1,
  },
  certValue: {
    fontSize: 14,
    color: '#0F172A',
    fontFamily: 'Inter-SemiBold',
    flex: 1,
    textAlign: 'right',
  },
  warningBox: {
    flexDirection: 'row',
    backgroundColor: '#FEF2F2',
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
    alignItems: 'flex-start',
  },
  warningText: {
    fontSize: 15,
    color: '#DC2626',
    fontFamily: 'Inter-Medium',
    marginLeft: 12,
    flex: 1,
    lineHeight: 22,
  },
  noteBox: {
    flexDirection: 'row',
    backgroundColor: '#FFFBEB',
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
    alignItems: 'flex-start',
  },
  noteText: {
    fontSize: 15,
    color: '#D97706',
    fontFamily: 'Inter-Medium',
    marginLeft: 12,
    flex: 1,
    lineHeight: 22,
  },
  ingredientsSection: {
    marginBottom: 32,
  },
  ingredientsTitle: {
    fontSize: 18,
    color: '#0F172A',
    fontFamily: 'Inter-SemiBold',
    marginBottom: 12,
    letterSpacing: -0.3,
  },
  ingredientsList: {
    fontSize: 15,
    color: '#64748B',
    fontFamily: 'Inter-Regular',
    lineHeight: 22,
  },
  scanAgainButton: {
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  scanAgainText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    letterSpacing: -0.3,
  },
  statusText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
    marginLeft: 12,
    letterSpacing: -0.3,
  },
  messageSection: {
    backgroundColor: '#FEF2F2',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#EF4444',
  },
  messageText: {
    fontSize: 15,
    color: '#DC2626',
    fontFamily: 'Inter-Medium',
    lineHeight: 22,
    textAlign: 'center',
  },
});



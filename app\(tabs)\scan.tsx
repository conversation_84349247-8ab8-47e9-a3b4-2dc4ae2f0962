import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Camera,
  FlipHorizontal,
  X,
  Shield,
  CircleCheck as CheckCircle,
  TriangleAlert as AlertTriangle,
  Info,
  Sparkles,
  ExternalLink,
  Search,
  Zap
} from 'lucide-react-native';
import { useRouter } from 'expo-router';

const { width, height } = Dimensions.get('window');

// Types for better type safety
interface ScanResult {
  data: string;
  message: string;
  status?: 'verified' | 'not_certified' | 'pending' | 'unknown';
}

interface QRCodeData {
  type: 'search' | 'url' | 'unknown';
  value: string;
  originalData: string;
}

export default function ScanScreen() {
  const [facing, setFacing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const [isScanning, setIsScanning] = useState(false);
  const [scanResult, setScanResult] = useState<ScanResult | null>(null);
  const [qrData, setQrData] = useState<QRCodeData | null>(null);
  const router = useRouter();

  // Helper function to parse QR code data
  const parseQRCode = useCallback((data: string): QRCodeData => {
    // Check if the QR code contains a search parameter
    const searchMatch = data.match(/search=([^&]+)/);
    if (searchMatch && searchMatch[1]) {
      return {
        type: 'search',
        value: decodeURIComponent(searchMatch[1]),
        originalData: data
      };
    }

    // Check if it's a valid URL for the certification sites
    const validDomains = [
      'https://certitracehalal786.fr',
      'https://directory.certitracehalal786.fr',
      'https://controle-certitrace.fr'
    ];

    if (validDomains.some(domain => data.includes(domain))) {
      return {
        type: 'url',
        value: data,
        originalData: data
      };
    }

    return {
      type: 'unknown',
      value: data,
      originalData: data
    };
  }, []);

  // Handle navigation based on QR code type
  const handleQRNavigation = useCallback((qrData: QRCodeData) => {
    switch (qrData.type) {
      case 'search':
        router.push(`/certifications?search=${qrData.value}`);
        break;
      case 'url':
        // For URLs, we could open in WebView or external browser
        // For now, navigate to certifications page
        router.push('/certifications');
        break;
      default:
        // Show error result
        setScanResult({
          data: qrData.originalData,
          message: 'Ce code QR ne correspond pas à un produit certiTracehalal786 dans notre base de données.',
          status: 'unknown'
        });
    }
  }, [router]);

  if (!permission) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <LinearGradient
            colors={['#059669', '#10B981']}
            style={styles.loadingIcon}>
            <Camera size={32} color="#FFFFFF" strokeWidth={2} />
          </LinearGradient>
          <Text style={styles.loadingText}>Chargement de la caméra...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!permission.granted) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.permissionContainer}>
          <LinearGradient
            colors={['#667eea', '#764ba2']}
            style={styles.permissionIcon}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}>
            <Camera size={56} color="#FFFFFF" strokeWidth={1.5} />
            <Sparkles size={24} color="#FFFFFF" style={styles.sparkleIcon} />
          </LinearGradient>
          <Text style={styles.permissionTitle}>Accès à la caméra requis</Text>
          <Text style={styles.permissionText}>
            Nous avons besoin de l'accès à votre caméra pour scanner le QR Code et vérifier
            la certification halal des produits en temps réel.
          </Text>
          <TouchableOpacity style={styles.permissionButton} onPress={requestPermission}>
            <LinearGradient
              colors={['#667eea', '#764ba2']}
              style={styles.buttonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}>
              <Text style={styles.permissionButtonText}>Autoriser l'accès</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const toggleCameraFacing = useCallback(() => {
    setFacing((current) => (current === 'back' ? 'front' : 'back'));
  }, []);

  const handleBarcodeScanned = useCallback(({ data }: { data: string }) => {
    if (isScanning || !data) return;

    setIsScanning(true);

    // Parse the QR code data
    const parsedData = parseQRCode(data);
    setQrData(parsedData);

    // Show scanning animation for a moment
    setTimeout(() => {
      setIsScanning(false);

      if (parsedData.type === 'search') {
        // Show success result for search codes
        setScanResult({
          data: `Produit trouvé: ${parsedData.value}`,
          message: 'Code QR valide détecté. Redirection vers les détails du produit...',
          status: 'verified'
        });

        // Navigate after showing result
        setTimeout(() => {
          handleQRNavigation(parsedData);
        }, 2000);
      } else if (parsedData.type === 'url') {
        // Show success result for URL codes
        setScanResult({
          data: 'Lien de certification détecté',
          message: 'Redirection vers le site de certification...',
          status: 'verified'
        });

        // Navigate after showing result
        setTimeout(() => {
          handleQRNavigation(parsedData);
        }, 2000);
      } else {
        // Show error for unknown codes
        setScanResult({
          data: parsedData.originalData,
          message: 'Ce code QR ne correspond pas à un produit certiTracehalal786 dans notre base de données.',
          status: 'unknown'
        });
      }
    }, 1500);
  }, [isScanning, parseQRCode, handleQRNavigation]);

  const resetScan = useCallback(() => {
    setScanResult(null);
    setIsScanning(false);
    setQrData(null);
  }, []);

  const getStatusColor = (status?: string): [string, string] => {
    switch (status) {
      case 'verified': return ['#10B981', '#059669'];
      case 'not_certified': return ['#EF4444', '#DC2626'];
      case 'pending': return ['#F59E0B', '#D97706'];
      case 'unknown': return ['#6B7280', '#4B5563'];
      default: return ['#10B981', '#059669'];
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'verified': return CheckCircle;
      case 'not_certified': return AlertTriangle;
      case 'pending': return Info;
      case 'unknown': return AlertTriangle;
      default: return CheckCircle;
    }
  };

  const getStatusText = (status?: string) => {
    switch (status) {
      case 'verified': return 'Produit Certifié';
      case 'not_certified': return 'Non Certifié';
      case 'pending': return 'Vérification en cours';
      case 'unknown': return 'Code QR non reconnu';
      default: return 'Résultat du scan';
    }
  };



  if (scanResult) {
    const StatusIcon = getStatusIcon(scanResult.status);
    const statusColors = getStatusColor(scanResult.status);

    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#000000" />
        <View style={styles.resultContainer}>
          <TouchableOpacity style={styles.closeButton} onPress={resetScan}>
            <X size={24} color="#64748B" strokeWidth={2} />
          </TouchableOpacity>

          <View style={styles.resultCard}>
            <LinearGradient
              colors={statusColors}
              style={styles.statusHeader}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}>
              <StatusIcon size={32} color="#FFFFFF" strokeWidth={2} />
              <Text style={styles.statusTitle}>{getStatusText(scanResult.status)}</Text>
            </LinearGradient>

            <View style={styles.productDetails}>
              <Text style={styles.productName}>{scanResult.data}</Text>

              {scanResult.message && (
                <View style={styles.messageSection}>
                  <Text style={styles.messageText}>{scanResult.message}</Text>
                </View>
              )}

              {qrData && (
                <View style={styles.qrDataSection}>
                  <Text style={styles.qrDataLabel}>Type de code:</Text>
                  <Text style={styles.qrDataValue}>
                    {qrData.type === 'search' ? 'Code produit' :
                     qrData.type === 'url' ? 'Lien de certification' : 'Code inconnu'}
                  </Text>

                  {qrData.type === 'search' && (
                    <>
                      <Text style={styles.qrDataLabel}>Numéro de lot:</Text>
                      <Text style={styles.qrDataValue}>{qrData.value}</Text>
                    </>
                  )}
                </View>
              )}

              <TouchableOpacity style={styles.scanAgainButton} onPress={resetScan}>
                <LinearGradient
                  colors={['#059669', '#10B981']}
                  style={styles.buttonGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}>
                  <Text style={styles.scanAgainText}>Scanner un autre produit</Text>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000000" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Scanner un produit</Text>
        <Text style={styles.headerSubtitle}>
          Pointez votre caméra sur le QR Code du produit pour vérifier sa certification halal
        </Text>
      </View>

      <View style={styles.cameraContainer}>
        <CameraView
          style={styles.camera}
          facing={facing}
          onBarcodeScanned={handleBarcodeScanned}
        >
          <View style={styles.overlay}>
            <View style={styles.scanFrame}>
              <View style={[styles.corner, styles.topLeft]} />
              <View style={[styles.corner, styles.topRight]} />
              <View style={[styles.corner, styles.bottomLeft]} />
              <View style={[styles.corner, styles.bottomRight]} />

              <LinearGradient
                colors={['#059669', '#10B981']}
                style={styles.scanLine}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              />
            </View>
          </View>

          <View style={styles.controls}>
            <TouchableOpacity style={styles.controlButton} onPress={toggleCameraFacing}>
              <LinearGradient
                colors={['rgba(0,0,0,0.6)', 'rgba(0,0,0,0.8)']}
                style={styles.controlButtonGradient}>
                <FlipHorizontal size={24} color="#FFFFFF" strokeWidth={2} />
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </CameraView>
      </View>

      {isScanning && (
        <View style={styles.scanningOverlay}>
          <View style={styles.scanningContainer}>
            <LinearGradient
              colors={['#059669', '#10B981']}
              style={styles.scanningIcon}>
              <Shield size={40} color="#FFFFFF" strokeWidth={2} />
            </LinearGradient>
            <Text style={styles.scanningText}>Vérification en cours...</Text>
            <Text style={styles.scanningSubtext}>
              Nous analysons le produit et vérifions sa certification halal
            </Text>
          </View>
        </View>
      )}

      <View style={styles.instructions}>
        <View style={styles.instructionCard}>
          <Zap size={20} color="#059669" strokeWidth={2} />
          <Text style={styles.instructionText}>
            Placez le QR Code dans le cadre pour le scanner automatiquement
          </Text>
        </View>

        {qrData && (
          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() => handleQRNavigation(qrData)}
          >
            <LinearGradient
              colors={['#059669', '#10B981']}
              style={styles.quickActionGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              <ExternalLink size={16} color="#FFFFFF" strokeWidth={2} />
              <Text style={styles.quickActionText}>
                {qrData.type === 'search' ? `Voir le produit: ${qrData.value}` : 'Ouvrir le lien'}
              </Text>
            </LinearGradient>
          </TouchableOpacity>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8FAFC',
  },
  loadingIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  loadingText: {
    fontSize: 16,
    color: '#64748B',
    fontFamily: 'Inter-Medium',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    backgroundColor: '#F8FAFC',
  },
  permissionIcon: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 32,
    position: 'relative',
  },
  sparkleIcon: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  permissionTitle: {
    fontSize: 28,
    color: '#0F172A',
    fontFamily: 'Inter-Bold',
    marginBottom: 16,
    textAlign: 'center',
    letterSpacing: -0.5,
  },
  permissionText: {
    fontSize: 16,
    color: '#64748B',
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 40,
    paddingHorizontal: 20,
  },
  permissionButton: {
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#667eea',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  buttonGradient: {
    paddingHorizontal: 40,
    paddingVertical: 18,
    alignItems: 'center',
  },
  permissionButtonText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    letterSpacing: -0.3,
  },
  header: {
    padding: 24,
    backgroundColor: '#F8FAFC',
  },
  headerTitle: {
    fontSize: 28,
    color: '#0F172A',
    fontFamily: 'Inter-Bold',
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#64748B',
    fontFamily: 'Inter-Regular',
    lineHeight: 24,
  },
  cameraContainer: {
    flex: 1,
    margin: 24,
    borderRadius: 24,
    overflow: 'hidden',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanFrame: {
    width: 300,
    height: 300,
    position: 'relative',
  },
  corner: {
    position: 'absolute',
    width: 50,
    height: 50,
    borderColor: '#FFFFFF',
    borderWidth: 4,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
    borderTopLeftRadius: 12,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
    borderTopRightRadius: 12,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
    borderBottomLeftRadius: 12,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    borderBottomRightRadius: 12,
  },
  scanLine: {
    position: 'absolute',
    top: '50%',
    left: 30,
    right: 30,
    height: 3,
    borderRadius: 2,
    opacity: 0.9,
  },
  controls: {
    position: 'absolute',
    bottom: 40,
    right: 40,
  },
  controlButton: {
    borderRadius: 28,
    overflow: 'hidden',
  },
  controlButtonGradient: {
    width: 56,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanningOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanningContainer: {
    backgroundColor: '#FFFFFF',
    padding: 48,
    borderRadius: 24,
    alignItems: 'center',
    margin: 40,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.3,
    shadowRadius: 24,
    elevation: 12,
  },
  scanningIcon: {
    width: 96,
    height: 96,
    borderRadius: 48,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  scanningText: {
    fontSize: 20,
    color: '#0F172A',
    fontFamily: 'Inter-Bold',
    marginBottom: 12,
    letterSpacing: -0.5,
  },
  scanningSubtext: {
    fontSize: 16,
    color: '#64748B',
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
    lineHeight: 24,
  },
  instructions: {
    padding: 24,
    backgroundColor: '#F8FAFC',
  },
  instructionText: {
    fontSize: 14,
    color: '#64748B',
    fontFamily: 'Inter-Medium',
    marginLeft: 12,
    lineHeight: 20,
    flex: 1,
  },
  resultContainer: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  closeButton: {
    position: 'absolute',
    top: 24,
    right: 24,
    zIndex: 1,
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  resultCard: {
    margin: 24,
    marginTop: 96,
    backgroundColor: '#FFFFFF',
    borderRadius: 24,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 12,
  },
  statusHeader: {
    padding: 32,
    alignItems: 'center',
  },
  statusTitle: {
    fontSize: 20,
    color: '#FFFFFF',
    fontFamily: 'Inter-Bold',
    marginTop: 16,
    textAlign: 'center',
    letterSpacing: -0.5,
  },
  productDetails: {
    padding: 32,
  },
  productName: {
    fontSize: 24,
    color: '#0F172A',
    fontFamily: 'Inter-Bold',
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  productBrand: {
    fontSize: 18,
    color: '#64748B',
    fontFamily: 'Inter-Medium',
    marginBottom: 32,
  },
  certificationInfo: {
    backgroundColor: '#F0FDF4',
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
  },
  certRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  certLabel: {
    fontSize: 14,
    color: '#64748B',
    fontFamily: 'Inter-Regular',
    flex: 1,
  },
  certValue: {
    fontSize: 14,
    color: '#0F172A',
    fontFamily: 'Inter-SemiBold',
    flex: 1,
    textAlign: 'right',
  },
  warningBox: {
    flexDirection: 'row',
    backgroundColor: '#FEF2F2',
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
    alignItems: 'flex-start',
  },
  warningText: {
    fontSize: 15,
    color: '#DC2626',
    fontFamily: 'Inter-Medium',
    marginLeft: 12,
    flex: 1,
    lineHeight: 22,
  },
  noteBox: {
    flexDirection: 'row',
    backgroundColor: '#FFFBEB',
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
    alignItems: 'flex-start',
  },
  noteText: {
    fontSize: 15,
    color: '#D97706',
    fontFamily: 'Inter-Medium',
    marginLeft: 12,
    flex: 1,
    lineHeight: 22,
  },
  ingredientsSection: {
    marginBottom: 32,
  },
  ingredientsTitle: {
    fontSize: 18,
    color: '#0F172A',
    fontFamily: 'Inter-SemiBold',
    marginBottom: 12,
    letterSpacing: -0.3,
  },
  ingredientsList: {
    fontSize: 15,
    color: '#64748B',
    fontFamily: 'Inter-Regular',
    lineHeight: 22,
  },
  scanAgainButton: {
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#059669',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  scanAgainText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    letterSpacing: -0.3,
  },
  statusText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
    marginLeft: 12,
    letterSpacing: -0.3,
  },
  messageSection: {
    backgroundColor: '#FEF2F2',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#EF4444',
  },
  messageText: {
    fontSize: 15,
    color: '#DC2626',
    fontFamily: 'Inter-Medium',
    lineHeight: 22,
    textAlign: 'center',
  },
  qrDataSection: {
    backgroundColor: '#F0FDF4',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#10B981',
  },
  qrDataLabel: {
    fontSize: 14,
    color: '#059669',
    fontFamily: 'Inter-SemiBold',
    marginBottom: 4,
    marginTop: 8,
  },
  qrDataValue: {
    fontSize: 16,
    color: '#0F172A',
    fontFamily: 'Inter-Medium',
    marginBottom: 8,
  },
  instructionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickActionButton: {
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#059669',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  quickActionGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
  },
  quickActionText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-SemiBold',
    fontSize: 14,
    letterSpacing: -0.2,
  },
});


